<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/navigation_home">

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.sun.englishlearning.screen.home.HomeFragment"
        android:label="@string/title_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/navigation_courses"
        android:name="com.sun.englishlearning.screen.courses.CoursesFragment"
        android:label="@string/title_courses"
        tools:layout="@layout/activity_lessons">
        <action
            android:id="@+id/action_courses_to_lesson_detail"
            app:destination="@id/lessonDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/lessonDetailFragment"
        android:name="com.sun.englishlearning.screen.lessondetail.LessonDetailFragment"
        android:label="Lesson Detail"
        tools:layout="@layout/fragment_lesson_detail">
        <argument
            android:name="lesson"
            app:argType="com.sun.englishlearning.data.model.Lesson" />
    </fragment>

    <fragment
        android:id="@+id/navigation_review"
        android:name="com.sun.englishlearning.screen.review.ReviewFragment"
        android:label="@string/title_review"
        tools:layout="@layout/fragment_review" />

    <fragment
        android:id="@+id/navigation_me"
        android:name="com.sun.englishlearning.screen.me.MeFragment"
        android:label="@string/title_me"
        tools:layout="@layout/fragment_me">
        <action
            android:id="@+id/action_navigation_me_to_navigation_profile"
            app:destination="@id/navigation_profile" />
    </fragment>

    <fragment
        android:id="@+id/navigation_profile"
        android:name="com.sun.englishlearning.screen.me.ProfileFragment"
        android:label="@string/profile_title"
        tools:layout="@layout/fragment_profile" />

</navigation>
