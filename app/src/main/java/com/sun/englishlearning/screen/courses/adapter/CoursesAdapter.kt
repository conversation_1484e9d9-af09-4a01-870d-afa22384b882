package com.sun.englishlearning.screen.courses.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.sun.englishlearning.R
import com.sun.englishlearning.databinding.ItemLessonCardBinding
import com.sun.englishlearning.data.model.Lesson

class CoursesAdapter(
    private var lessons: List<Lesson> = emptyList(),
    private val onLessonClick: (Lesson) -> Unit = {}
) : RecyclerView.Adapter<CoursesAdapter.LessonViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LessonViewHolder {
        val binding = ItemLessonCardBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return LessonViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>onViewHolder, position: Int) {
        holder.bind(lessons[position])
    }

    override fun getItemCount(): Int = lessons.size

    fun updateLessons(newLessons: List<Lesson>) {
        lessons = newLessons
        notifyDataSetChanged()
    }

    inner class LessonViewHolder(private val binding: ItemLessonCardBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(lesson: Lesson) {
            binding.apply {
                // Set lesson title
                textLessonTitle.text = lesson.title
                
                // Set lesson number
                textLessonNumber.text = "Lesson: ${lesson.lessonNumber}"
                
                // Set advanced level
                textAdvancedLevel.text = "Advanced: ${lesson.advancedLevel}"
                
                // Set points
                textLessonPoints.text = "points: ${lesson.currentPoints} / ${lesson.totalPoints}"
                
                // Set progress
                progressLesson.progress = lesson.progressPercentage

                // Load lesson image using Glide
                if (lesson.imageUrl.isNotEmpty()) {
                    Glide.with(binding.root.context)
                        .load(lesson.imageUrl)
                        .apply(RequestOptions().transform(RoundedCorners(24)))
                        .placeholder(R.drawable.ic_launcher_background)
                        .error(R.drawable.ic_launcher_background)
                        .into(imageLesson)
                } else {
                    imageLesson.setImageResource(R.drawable.ic_launcher_background)
                }

                // Set click listener
                root.setOnClickListener {
                    onLessonClick(lesson)
                }
            }
        }
    }
}
